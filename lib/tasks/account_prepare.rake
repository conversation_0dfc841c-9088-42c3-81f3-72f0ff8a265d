
# USAGE:
# bundle exec rake 'account_prepare:language[account_id]'
# bundle exec rake 'account_prepare:user_groups[account_id]'
# bundle exec rake 'account_prepare:leader_boards[account_id]'
# bundle exec rake 'account_prepare:levels[account_id]'
# bundle exec rake 'account_prepare:level_icons[account_id]'
# bundle exec rake 'account_prepare:user_activity[account_id]'
# bundle exec rake 'account_prepare:doorkeeper_app[account_id]'
# bundle exec rake 'account_prepare:kudos[account_id]'
# bundle exec rake 'account_prepare:market_categories[account_id]'
# or; (for all)
# bundle exec rake 'account_prepare:defaults[account_id]'

namespace :account_prepare do
  desc "Setup account's default records for languages"
  task :defaults, [:account_id] => :environment do |_, args|
    # Find account
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_languages
      puts "Language records created! For #{account.slug}"

      setup_user_groups
      puts "User Group records created! For #{account.slug}"

      setup_leader_boards
      puts "Leader Board records created! For #{account.slug}"

      setup_levels
      puts "Level records created! For #{account.slug}"

      setup_market_categories
      puts "Market Categories created! For #{account.slug}"

      setup_kudos
      puts "setup_kudos records created! For #{account.slug}"

      setup_doorkeeper_app
      puts "Doorkeeper application record created! For #{account.slug}"
    end
  end

  desc "Setup account's default records for languages"
  task :language, [:account_id] => :environment do |_, args|
    # Find account
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_languages
      puts "Language records created! For #{account.slug}"
    end
  end

  desc "Setup account's default records for user groups"
  task :user_groups, [:account_id] => :environment do |_, args|
    # Find account
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_user_groups
      puts "User Group records created! For #{account.slug}"
    end
  end

  desc "Setup account's default records for leader boards"
  task :leader_boards, [:account_id] => :environment do |_, args|
    # Find account
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_leader_boards
      puts "Leader Board records created! For #{account.slug}"
    end
  end

  desc "Setup account's default records for levels"
  task :levels, [:account_id] => :environment do |_, args|
    # Find account
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_levels
      puts "Level records created! For #{account.slug}"
    end
  end

  desc "Setup account's default icons for levels"
  task :level_icons, [:account_id] => :environment do |_, args|
    # Find account
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_level_icons
      puts "Level icons created! For #{account.slug}"
    end
  end

  desc "Setup account's default activity for users"
  task :user_activity, [:account_id] => :environment do |_, args|
    # Find account
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_user_activity
      puts "Level icons created! For #{account.slug}"
    end
  end

  desc "Setup account's default records for doorkeeper application"
  task :doorkeeper_app, [:account_id] => :environment do |_, args|
    # Find account
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_doorkeeper_app(name: "Mobile Client for #{account.slug}")
      puts "Doorkeeper application created! For #{account.slug}"
    end
  end

  desc "Setup account's default records for kudos"
  task :kudos, [:account_id] => :environment do |_, args|
    # Find account
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_kudos
      puts "[#{account.slug}] Importing default kudos!"
    end
  end

  desc "Setup account's default records for market categories"
  task :market_categories, [:account_id] => :environment do |_, args|
    account = Account.find(args[:account_id].to_i)

    ActiveRecord::Base.connected_to(role: :writing, shard: account.shard.to_sym) do
      setup_market_categories
      puts "[#{account.slug}] Importing default market_categories!"
    end
  end

  def setup_market_categories
    file_path = Rails.root.join('public','example_files','Default Market Categories List.xlsx')
    icons_dir = Rails.root.join('public','example_files','Market Categories Icons')

    sheet = Roo::Excelx.new(file_path).sheet(0)
    verify_market_header!(sheet.row(1))

    created = 0
    sheet.each_row_streaming(offset: 1) do |row|
      data = parse_market_row(row)
      puts "Processing #{data.tr_name.inspect} (active: #{data.active_flag})"

      mc = build_market_category(data)
      attach_icon(mc, icons_dir.join(data.icon_name))

      if mc.save
        created += 1
        puts "Saved #{mc.name}"
      else
        warn "[#{data.tr_name}] #{mc.errors.full_messages.join(', ')}"
      end
    end

    puts "Imported #{created} market categories"
  end

  def setup_languages
    # Setup Languages
    Language.find_or_create_by!(flag_code: 'GB', language_code: 'EN', app_language: true,
                                platform_language: true, default_language: true)
    Language.find_or_create_by!(flag_code: 'TR', language_code: 'TR', app_language: true,
                                platform_language: true, default_language: false)
  end

  def setup_doorkeeper_app(name: 'Mobile Client')
    Doorkeeper::Application.find_or_create_by!(
      name: name,
      uid: Rails.application.credentials.dig(:doorkeeper, :application, :client_id),
      secret: Rails.application.credentials.dig(:doorkeeper, :application, :client_secret)
    )
  end

  def setup_user_groups
    SubGroup::USER_FIELD_NAMES.each do |field_name|
      name = field_name.eql?(:id) ? 'Users' : field_name.to_s.titleize
      user_group = UserGroup.find_or_create_by!(name: name, description: name, default: true)

      # Create sub group of default user group
      user_group.sub_groups.create(table_name: :user, field_name: field_name, values: [])
    end

    SubGroup::ORGANIZATION_FIELD_NAMES.each do |field_name|
      name = field_name.to_s.titleize
      user_group = UserGroup.find_or_create_by!(name: name, description: name, default: true)

      # Create sub group of default user group
      user_group.sub_groups.find_or_create_by!(
        table_name: :organization, field_name: field_name, values: [])
    end
  end

  def setup_leader_boards
    [:challenge_point,
     :challenge_win,
     :challenge_vote,
     :challenge_lose].each do |order_type|
      LeaderBoard.find_or_create_by!(order_type: order_type)
    end
  end

  def setup_levels
    levels_count = (25 - Level.count)

    levels_count.times do
      index = Level.count + 1

      level = Level.new(
        name_en: "Level #{index}",
        description_en: "Level #{index}",
        language_code_en: :en,
        name_tr: "Seviye #{index}",
        description_tr: "Seviye #{index}",
        language_code_tr: :tr
      )

      level.save(validate: false)
    end

    # Create default icons for levels
    setup_level_icons
  end

  def setup_level_icons
    Level.all.each do |level|
      level.icon.attach(io: File.open(level.default_icon_path),
                        filename: File.basename(level.default_icon_path))
      level.save(validate: false)
    end
  end

  def setup_user_activity
    User.all.each do |user|
      UserActivity.find_or_create_by(user: user) do |activity|
        activity.level = Level.first
      end
    end
  end

  # -begins of KUDOS
  def setup_kudos
    excel_path = Rails.root.join('public','example_files','Default Kudos List.xlsx')
    icons_dir  = Rails.root.join('public','example_files','Beeply_Kudos')

    sheet = Roo::Excelx.new(excel_path).sheet(0)
    verify_header!(sheet.row(1))

    created = 0
    sheet.each_row_streaming(offset: 1) do |row|
      data = parse_row(row)
      puts "Processing #{data.tr_name.inspect}"

      kudo = build_kudo(data)
      build_limit(kudo, data)

      if kudo.save
        created += 1
        attach_icon(kudo, icons_dir.join(data.icon_name))
        puts "Saved #{data.tr_name} (#{data.from_role}->#{data.to_role})"
      else
        warn "[#{data.tr_name}] #{kudo.errors.full_messages.join(', ')}"
      end
    end

    puts "Imported #{created} kudos"
  end

  # Validate header row
  def verify_header!(header)
    expected_columns = %w[
      Name Description Category Language\ Code
      Name Description Category Language\ Code
      İcon From To Limit\ Period
    ]
    header_strings = header.map { |col| col.to_s }
    missing_columns = expected_columns - header_strings
    unless missing_columns.empty?
      abort("Missing columns: #{missing_columns.join(', ')}")
    end
  end

  # Convert rows to small struct
  def parse_row(row)
    raw = row.map { |c| c.cell_value.to_s.strip }
    OpenStruct.new(
      tr_name:       raw[0],
      tr_desc:       raw[1],
      tr_cat:        raw[2],
      tr_lang:       locale_code(raw[3]),
      en_name:       raw[4],
      en_desc:       raw[5],
      en_cat:        raw[6],
      en_lang:       locale_code(raw[7]),
      icon_name:     raw[8],
      from_role:     raw[9].downcase,
      to_role:       raw[10].downcase,
      period_type:   raw[11].downcase
    )
  end

  # Build the Kudo with its mobility
  def build_kudo(data)
    k = Kudo.i18n.find_by(name: data.tr_name, locale: :tr) ||
        Kudo.i18n.find_by(name: data.en_name, locale: :en) ||
        Kudo.new

    I18n.with_locale(:tr) do
      k.name          = data.tr_name
      k.description   = data.tr_desc
      k.category      = data.tr_cat
      k.language_code = data.tr_lang
    end
    I18n.with_locale(:en) do
      k.name          = data.en_name
      k.description   = data.en_desc
      k.category      = data.en_cat
      k.language_code = data.en_lang
    end
    k
  end

  # Build KudoLimit before kudo saving
  def build_limit(kudo, data)
    kudo.kudo_limits.find_or_initialize_by(
      from_role:   data.from_role,
      to_role:     data.to_role,
      period_type: data.period_type,
    )
  end
  # -ends of KUDOS

  # -begins of MARKET CATEGORIES
  def verify_market_header!(header)
    expected = %w[
      Name Description Language\ Code
      Name Description Language\ Code
      Icon Active
    ]
    header_strings = header.map(&:to_s)
    missing = expected - header_strings
    abort("Missing columns: #{missing.join(', ')}") unless missing.empty?
  end

  def parse_market_row(row)
    raw = row.map { |c| c.cell_value.to_s.strip }
    OpenStruct.new(
      tr_name:     raw[0],
      tr_desc:     raw[1],
      tr_lang:     locale_code(raw[2]),
      en_name:     raw[3],
      en_desc:     raw[4],
      en_lang:     locale_code(raw[5]),
      icon_name:   raw[6],
      active_flag: raw[7]
    )
  end

  def build_market_category(data)
    mc = MarketCategory.i18n.find_or_initialize_by(name: data.en_name)
    active = data.active_flag.to_s.strip.downcase

    # TR Localize
    I18n.with_locale(:tr) do
      mc.name          = data.tr_name
      mc.description   = data.tr_desc
      mc.language_code = data.tr_lang
      mc.is_active     = active
    end

    # EN Localize
    I18n.with_locale(:en) do
      mc.name          = data.en_name
      mc.description   = data.en_desc
      mc.language_code = data.en_lang
      mc.is_active     = active
    end

    mc
  end
  # -ends of MARKET CATEGORIES

  def locale_code(name)
    case name.to_s.strip.downcase
    when 'turkish' then 'tr'
    when 'english' then 'en'
    else               name.to_s.strip.downcase[0,2]
    end
  end

  def attach_icon(record, icon_path)
    if File.exist?(icon_path)
      record.icon.attach(io: File.open(icon_path,'rb'), filename: icon_path.basename.to_s)
    else
      warn "Icon missing: #{icon_path.basename}"
    end
  end
end
