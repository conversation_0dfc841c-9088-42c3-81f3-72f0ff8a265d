# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.2].define(version: 2025_09_12_102210) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "hstore"
  enable_extension "plpgsql"

  create_table "achievement_gradual_users", force: :cascade do |t|
    t.bigint "achievement_gradual_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "completed_at"
    t.float "score"
    t.index ["achievement_gradual_id", "user_id"], name: "idx_on_achievement_gradual_id_user_id_c870f9f7a3", unique: true
    t.index ["achievement_gradual_id"], name: "index_achievement_gradual_users_on_achievement_gradual_id"
    t.index ["user_id"], name: "index_achievement_gradual_users_on_user_id"
  end

  create_table "achievement_graduals", force: :cascade do |t|
    t.bigint "achievement_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "target"
    t.jsonb "log_data"
    t.index ["achievement_id"], name: "index_achievement_graduals_on_achievement_id"
  end

  create_table "achievement_sources", force: :cascade do |t|
    t.integer "mission_target_action"
    t.bigint "achievement_id", null: false
    t.string "sourceable_type", null: false
    t.bigint "sourceable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
    t.index ["achievement_id"], name: "index_achievement_sources_on_achievement_id"
    t.index ["sourceable_type", "sourceable_id"], name: "index_achievement_sources_on_sourceable"
  end

  create_table "achievement_subscriptions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "achievement_id", null: false
    t.bigint "gradual_level_id"
    t.datetime "score_updated_at"
    t.boolean "is_completed", default: false
    t.datetime "completed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "score"
    t.jsonb "log_data"
    t.index ["achievement_id", "user_id"], name: "index_achievement_subscriptions_on_achievement_id_and_user_id", unique: true
    t.index ["achievement_id"], name: "index_achievement_subscriptions_on_achievement_id"
    t.index ["gradual_level_id"], name: "index_achievement_subscriptions_on_gradual_level_id"
    t.index ["user_id"], name: "index_achievement_subscriptions_on_user_id"
  end

  create_table "achievements", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.string "hint"
    t.boolean "is_visible"
    t.datetime "start_from"
    t.datetime "end_to"
    t.integer "prize_earn_type", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
  end

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "announcements", force: :cascade do |t|
    t.string "header"
    t.text "description"
    t.datetime "publish_date"
    t.boolean "is_draft", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "audience_groups", force: :cascade do |t|
    t.string "targetable_type", null: false
    t.bigint "targetable_id", null: false
    t.integer "statement"
    t.integer "table_name"
    t.integer "field_name"
    t.text "values", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["targetable_type", "targetable_id"], name: "index_audience_groups_on_targetable"
  end

  create_table "badges", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.boolean "is_active", default: true
    t.integer "badge_type", default: 0, null: false
    t.datetime "start_from"
    t.datetime "end_to"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "beeply_metrics", force: :cascade do |t|
    t.bigint "data_channel_id", null: false
    t.bigint "calculate_from_id", null: false
    t.jsonb "advanced_filter", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "last_run_at"
    t.index ["calculate_from_id"], name: "index_beeply_metrics_on_calculate_from_id"
    t.index ["data_channel_id"], name: "index_beeply_metrics_on_data_channel_id"
  end

  create_table "celebrations", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "celebration_type", null: false
    t.datetime "event_date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["celebration_type"], name: "index_celebrations_on_celebration_type"
    t.index ["user_id", "celebration_type", "event_date"], name: "index_celebrations_on_user_type_event_date", unique: true
    t.index ["user_id"], name: "index_celebrations_on_user_id"
  end

  create_table "challenge_options", force: :cascade do |t|
    t.string "commitment", null: false
    t.bigint "mission_id"
    t.bigint "mission_award_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
    t.index ["mission_award_id"], name: "index_challenge_options_on_mission_award_id"
    t.index ["mission_id"], name: "index_challenge_options_on_mission_id"
  end

  create_table "challenge_settings", force: :cascade do |t|
    t.integer "challenge_win_amount"
    t.integer "challenge_draw_amount"
    t.integer "challenge_lost_amount"
    t.integer "vote_win_amount"
    t.integer "vote_draw_amount"
    t.integer "vote_lost_amount"
    t.integer "vote_vote_amount"
    t.bigint "vote_point_id", null: false
    t.bigint "challenge_point_id", null: false
    t.index ["challenge_point_id"], name: "index_challenge_settings_on_challenge_point_id"
    t.index ["vote_point_id"], name: "index_challenge_settings_on_vote_point_id"
  end

  create_table "data_sources", force: :cascade do |t|
    t.integer "status"
    t.integer "data_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "default_settings", force: :cascade do |t|
    t.bigint "level_point_id", null: false
    t.bigint "language_id", null: false
    t.string "time_zone"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["language_id"], name: "index_default_settings_on_language_id"
    t.index ["level_point_id"], name: "index_default_settings_on_level_point_id"
  end

  create_table "dr_category_based_purchases", force: :cascade do |t|
    t.datetime "start_date"
    t.datetime "end_date"
    t.integer "interval", default: 0
    t.integer "status"
    t.datetime "last_updated_at"
    t.jsonb "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "dr_mission_statistics", force: :cascade do |t|
    t.datetime "start_date"
    t.datetime "end_date"
    t.integer "interval", default: 0
    t.integer "status"
    t.integer "coming_soon_missions_count", default: 0
    t.integer "in_progress_missions_count", default: 0
    t.integer "completed_missions_count", default: 0
    t.datetime "last_updated_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "dr_point_distributions", force: :cascade do |t|
    t.datetime "start_date"
    t.datetime "end_date"
    t.integer "interval", default: 0
    t.integer "status"
    t.datetime "last_updated_at"
    t.jsonb "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "dr_popular_kudos", force: :cascade do |t|
    t.datetime "start_date"
    t.datetime "end_date"
    t.datetime "last_updated_at"
    t.integer "interval", default: 0
    t.integer "status"
    t.jsonb "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "dr_product_based_purchases", force: :cascade do |t|
    t.datetime "start_date"
    t.datetime "end_date"
    t.integer "interval", default: 0
    t.integer "status"
    t.datetime "last_updated_at"
    t.jsonb "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "dr_user_onboardings", force: :cascade do |t|
    t.datetime "start_date"
    t.datetime "end_date"
    t.datetime "last_updated_at"
    t.integer "interval", default: 0
    t.integer "status"
    t.integer "passive_users_count"
    t.integer "total_users_count"
    t.integer "active_users_count"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "entity_fields", force: :cascade do |t|
    t.bigint "entity_type_id", null: false
    t.string "name", null: false
    t.string "label"
    t.text "description"
    t.integer "datatype", null: false
    t.boolean "required", default: false
    t.string "default_value"
    t.jsonb "properties", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_primary_key", default: false, null: false
    t.boolean "is_calculated_field", default: false, null: false
    t.index ["entity_type_id"], name: "index_entity_fields_on_entity_type_id"
  end

  create_table "entity_record_values", force: :cascade do |t|
    t.bigint "entity_record_id", null: false
    t.bigint "entity_field_id", null: false
    t.string "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["entity_field_id"], name: "index_entity_record_values_on_entity_field_id"
    t.index ["entity_record_id"], name: "index_entity_record_values_on_entity_record_id"
  end

  create_table "entity_records", force: :cascade do |t|
    t.bigint "entity_type_id", null: false
    t.string "external_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["entity_type_id"], name: "index_entity_records_on_entity_type_id"
  end

  create_table "entity_types", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "primary_user_id"
    t.boolean "is_draft", default: true
    t.text "description"
    t.integer "primary_user_mapping_field"
    t.index ["name"], name: "index_entity_types_on_name", unique: true
    t.index ["primary_user_id"], name: "index_entity_types_on_primary_user_id"
  end

  create_table "event_notifications", force: :cascade do |t|
    t.string "notifyable_type", null: false
    t.bigint "notifyable_id", null: false
    t.string "first_title"
    t.text "first_body"
    t.string "second_title"
    t.text "second_body"
    t.integer "trigger_event"
    t.index ["notifyable_type", "notifyable_id"], name: "index_event_notifications_on_notifyable"
  end

  create_table "faqs", force: :cascade do |t|
    t.string "question"
    t.text "answer"
    t.string "category"
    t.datetime "publish_date"
    t.boolean "is_draft", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "fetch_posts", force: :cascade do |t|
    t.bigint "social_media_feed_id", null: false
    t.string "content_id"
    t.string "thumbnail_url"
    t.string "title"
    t.text "description"
    t.datetime "published_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["social_media_feed_id", "content_id"], name: "index_fetch_posts_on_feed_id_and_content_id", unique: true
    t.index ["social_media_feed_id"], name: "index_fetch_posts_on_social_media_feed_id"
  end

  create_table "friendship_challenge_votes", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "friendship_challenge_id", null: false
    t.integer "prediction", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
    t.index ["friendship_challenge_id"], name: "index_friendship_challenge_votes_on_friendship_challenge_id"
    t.index ["user_id", "friendship_challenge_id"], name: "idx_on_user_id_friendship_challenge_id_9b3cbb780b", unique: true
    t.index ["user_id"], name: "index_friendship_challenge_votes_on_user_id"
  end

  create_table "friendship_challenges", force: :cascade do |t|
    t.string "resourceable_type", null: false
    t.bigint "resourceable_id", null: false
    t.integer "status", default: 0
    t.datetime "completed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "challenger_id", null: false
    t.bigint "challenging_id", null: false
    t.bigint "winner_id"
    t.jsonb "log_data"
    t.index ["challenger_id", "challenging_id", "resourceable_id", "resourceable_type"], name: "index_friendship_challenges_on_challenger_challenging_resource", unique: true
    t.index ["challenging_id", "challenger_id", "resourceable_id", "resourceable_type"], name: "index_friendship_challenges_on_challenging_challenger_resource", unique: true
    t.index ["resourceable_type", "resourceable_id"], name: "index_friendship_challenges_on_resourceable"
  end

  create_table "friendship_requests", force: :cascade do |t|
    t.bigint "requestor_id", null: false
    t.bigint "receiver_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["receiver_id"], name: "index_friendship_requests_on_receiver_id"
    t.index ["requestor_id", "receiver_id"], name: "index_friendship_requests_on_requestor_and_receiver_pending", unique: true, where: "(status = 0)"
    t.index ["requestor_id"], name: "index_friendship_requests_on_requestor_id"
  end

  create_table "friendships", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "friend_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["friend_id"], name: "index_friendships_on_friend_id"
    t.index ["user_id", "friend_id"], name: "index_friendships_on_user_id_and_friend_id", unique: true
    t.index ["user_id"], name: "index_friendships_on_user_id"
  end

  create_table "integration_excel_accesses", force: :cascade do |t|
    t.string "email"
    t.string "token"
    t.string "refresh_token"
    t.datetime "expires_at"
    t.boolean "expires"
    t.bigint "creator_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_integration_excel_accesses_on_creator_id"
  end

  create_table "integration_excel_workbooks", force: :cascade do |t|
    t.string "drive_item_id"
    t.string "drive_item_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "integration_import_achievement_data", force: :cascade do |t|
    t.string "value"
    t.bigint "user_id", null: false
    t.bigint "integration_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["integration_id"], name: "index_integration_import_achievement_data_on_integration_id"
    t.index ["user_id", "integration_id"], name: "idx_on_user_id_integration_id_1ca5030fd9", unique: true
    t.index ["user_id"], name: "index_integration_import_achievement_data_on_user_id"
  end

  create_table "integration_import_kpi_data", force: :cascade do |t|
    t.string "actual"
    t.string "target"
    t.bigint "user_id", null: false
    t.bigint "integration_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["integration_id"], name: "index_integration_import_kpi_data_on_integration_id"
    t.index ["user_id", "integration_id"], name: "idx_on_user_id_integration_id_8c519da334", unique: true
    t.index ["user_id"], name: "index_integration_import_kpi_data_on_user_id"
  end

  create_table "integration_import_mission_data", force: :cascade do |t|
    t.string "value"
    t.bigint "user_id", null: false
    t.bigint "integration_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["integration_id"], name: "index_integration_import_mission_data_on_integration_id"
    t.index ["user_id", "integration_id"], name: "idx_on_user_id_integration_id_7e03bfd44f", unique: true
    t.index ["user_id"], name: "index_integration_import_mission_data_on_user_id"
  end

  create_table "integration_periods", force: :cascade do |t|
    t.bigint "integration_id", null: false
    t.boolean "is_auto_refresh", default: false
    t.integer "interval", null: false
    t.string "days_of_week", default: [], null: false, array: true
    t.integer "start_time", null: false
    t.integer "end_time", null: false
    t.string "time_zone", null: false
    t.datetime "last_run_at"
    t.integer "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["integration_id"], name: "index_integration_periods_on_integration_id"
  end

  create_table "integrations", force: :cascade do |t|
    t.integer "data_source"
    t.integer "data_type", null: false
    t.string "integrable_type"
    t.bigint "integrable_id"
    t.string "accessor_type"
    t.bigint "accessor_id"
    t.boolean "is_active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "metric_type"
    t.integer "audience_type", default: 0, null: false
    t.index ["accessor_type", "accessor_id"], name: "index_integrations_on_accessor"
    t.index ["integrable_type", "integrable_id"], name: "index_integrations_on_integrable"
  end

  create_table "kpi_related_missions", force: :cascade do |t|
    t.bigint "kpi_id", null: false
    t.bigint "mission_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["kpi_id"], name: "index_kpi_related_missions_on_kpi_id"
    t.index ["mission_id"], name: "index_kpi_related_missions_on_mission_id"
  end

  create_table "kpis", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.boolean "is_active"
    t.integer "template"
    t.string "sourceable_type"
    t.bigint "sourceable_id"
    t.string "period_name"
    t.integer "metric_type"
    t.integer "first_level"
    t.integer "second_level"
    t.integer "third_level"
    t.string "first_level_name"
    t.string "second_level_name"
    t.string "third_level_name"
    t.index ["sourceable_type", "sourceable_id"], name: "index_kpis_on_sourceable"
  end

  create_table "kudo_limits", force: :cascade do |t|
    t.bigint "kudo_id", null: false
    t.integer "from_role", null: false
    t.integer "to_role", null: false
    t.integer "period_type", null: false
    t.integer "limit"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "current", default: 0
    t.index ["kudo_id"], name: "index_kudo_limits_on_kudo_id"
  end

  create_table "kudos", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.boolean "is_active", default: true
    t.string "category"
    t.datetime "start_from"
    t.datetime "end_to"
    t.integer "user_kudos_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "languages", force: :cascade do |t|
    t.string "flag_code", null: false
    t.string "language_code", null: false
    t.boolean "app_language", default: false
    t.boolean "platform_language", default: false
    t.boolean "default_language", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["flag_code", "language_code"], name: "index_languages_on_flag_code_and_language_code", unique: true
  end

  create_table "leader_boards", force: :cascade do |t|
    t.boolean "is_reverse"
    t.boolean "is_active"
    t.boolean "is_zero_visible"
    t.boolean "is_privacy"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "order_type"
    t.string "sourceable_type"
    t.bigint "sourceable_id"
    t.index ["sourceable_type", "sourceable_id"], name: "index_leader_boards_on_sourceable"
  end

  create_table "level_awards", force: :cascade do |t|
    t.bigint "level_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
    t.index ["level_id"], name: "index_level_awards_on_level_id"
  end

  create_table "levels", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.boolean "is_draft", default: true
    t.integer "achievements_count", default: 0, null: false
    t.integer "badges_count", default: 0, null: false
    t.integer "enps_replies_count", default: 0, null: false
    t.integer "kudos_count", default: 0, null: false
    t.integer "social_posts_count", default: 0, null: false
    t.integer "vote_challanges_count", default: 0, null: false
    t.integer "win_challanges_count", default: 0, null: false
    t.integer "survey_replies_count", default: 0, null: false
    t.integer "feedbacks_count", default: 0, null: false
    t.integer "likes_count", default: 0, null: false
    t.integer "start_challanges_count", default: 0, null: false
    t.integer "mission_completes_count", default: 0, null: false
    t.integer "add_friends_count", default: 0, null: false
    t.integer "points_count", default: 0, null: false
    t.integer "mini_game_points_count", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
  end

  create_table "market_categories", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.boolean "is_active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "mission_awards", force: :cascade do |t|
    t.bigint "mission_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_challenge", default: false, null: false
    t.string "min_win_rate"
    t.jsonb "log_data"
    t.index ["mission_id"], name: "index_mission_awards_on_mission_id"
  end

  create_table "mission_periods", force: :cascade do |t|
    t.bigint "main_mission_id", null: false
    t.integer "repeat_type"
    t.integer "repeat_every"
    t.integer "times"
    t.integer "duration"
    t.date "starting_on"
    t.integer "starting_at"
    t.integer "ending_at"
    t.integer "starting_wday"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "are_child_missions_generated", default: false
    t.jsonb "log_data"
    t.index ["main_mission_id"], name: "index_mission_periods_on_main_mission_id"
  end

  create_table "mission_subscriptions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "subscribable_type", null: false
    t.bigint "subscribable_id", null: false
    t.datetime "score_updated_at"
    t.boolean "is_completed", default: false
    t.datetime "completed_at"
    t.float "target"
    t.float "score"
    t.float "success_rate"
    t.jsonb "log_data"
    t.boolean "is_rewarded", default: false
    t.string "process_type", default: "integration"
    t.index ["subscribable_id", "subscribable_type", "user_id"], name: "index_mission_subscriptions_on_subscribable_and_user", unique: true, where: "((process_type)::text = 'integration'::text)"
    t.index ["subscribable_type", "subscribable_id"], name: "index_mission_subscriptions_on_subscribable"
    t.index ["user_id"], name: "index_mission_subscriptions_on_user_id"
  end

  create_table "mission_targets", force: :cascade do |t|
    t.bigint "mission_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "value"
    t.jsonb "log_data"
    t.index ["mission_id"], name: "index_mission_targets_on_mission_id"
  end

  create_table "missions", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.text "condition_description"
    t.boolean "use_kpi", default: false
    t.boolean "use_leader_board", default: false
    t.boolean "use_achievement", default: false
    t.integer "audience_type"
    t.integer "formula_type"
    t.boolean "is_visible", default: false
    t.boolean "is_soon", default: false
    t.boolean "is_repeat", default: false
    t.integer "prize_earning_type"
    t.integer "challenge_ability"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_draft", default: true
    t.integer "buffer_time"
    t.boolean "has_challenges"
    t.bigint "main_mission_id"
    t.bigint "next_mission_id"
    t.bigint "previous_mission_id"
    t.boolean "is_main_mission", default: false, null: false
    t.datetime "start_from"
    t.datetime "end_to"
    t.string "sourceable_type"
    t.bigint "sourceable_id"
    t.integer "commitment_type"
    t.jsonb "log_data"
    t.integer "award_type", default: 0
    t.integer "award_distribution_rule"
    t.integer "award_distribution_time_type", default: 0
    t.index ["main_mission_id"], name: "index_missions_on_main_mission_id"
    t.index ["next_mission_id"], name: "index_missions_on_next_mission_id"
    t.index ["previous_mission_id"], name: "index_missions_on_previous_mission_id"
    t.index ["sourceable_type", "sourceable_id"], name: "index_missions_on_sourceable"
    t.index ["start_from", "end_to", "is_draft"], name: "index_missions_on_dates_and_status"
  end

  create_table "mobility_string_translations", force: :cascade do |t|
    t.string "locale", null: false
    t.string "key", null: false
    t.string "value"
    t.string "translatable_type"
    t.bigint "translatable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["translatable_id", "translatable_type", "key"], name: "index_mobility_string_translations_on_translatable_attribute"
    t.index ["translatable_id", "translatable_type", "locale", "key"], name: "index_mobility_string_translations_on_keys", unique: true
    t.index ["translatable_type", "key", "value", "locale"], name: "index_mobility_string_translations_on_query_keys"
  end

  create_table "mobility_text_translations", force: :cascade do |t|
    t.string "locale", null: false
    t.string "key", null: false
    t.text "value"
    t.string "translatable_type"
    t.bigint "translatable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["translatable_id", "translatable_type", "key"], name: "index_mobility_text_translations_on_translatable_attribute"
    t.index ["translatable_id", "translatable_type", "locale", "key"], name: "index_mobility_text_translations_on_keys", unique: true
  end

  create_table "nft_certificates", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "primary_nft_certificate_id"
    t.string "qr_code", null: false
    t.integer "status", default: 0, null: false
    t.datetime "issue_date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "nft_id"
    t.string "ipfs_link"
    t.datetime "mint_date"
    t.index ["nft_id"], name: "index_nft_certificates_on_nft_id", unique: true
    t.index ["primary_nft_certificate_id"], name: "index_nft_certificates_on_primary_nft_certificate_id", unique: true
    t.index ["qr_code"], name: "index_nft_certificates_on_qr_code", unique: true
    t.index ["user_id"], name: "index_nft_certificates_on_user_id"
  end

  create_table "notifications", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.boolean "is_read", default: false
    t.string "title", null: false
    t.string "description", null: false
    t.string "notifyable_type", null: false
    t.bigint "notifyable_id", null: false
    t.jsonb "data", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_notifications_on_notifyable"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "oauth_access_grants", force: :cascade do |t|
    t.bigint "resource_owner_id", null: false
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.integer "expires_in", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.index ["application_id"], name: "index_oauth_access_grants_on_application_id"
    t.index ["resource_owner_id"], name: "index_oauth_access_grants_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_grants_on_token", unique: true
  end

  create_table "oauth_access_tokens", force: :cascade do |t|
    t.bigint "resource_owner_id"
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.string "refresh_token"
    t.integer "expires_in"
    t.string "scopes"
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.string "previous_refresh_token", default: "", null: false
    t.index ["application_id"], name: "index_oauth_access_tokens_on_application_id"
    t.index ["refresh_token"], name: "index_oauth_access_tokens_on_refresh_token", unique: true
    t.index ["resource_owner_id"], name: "index_oauth_access_tokens_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_tokens_on_token", unique: true
  end

  create_table "oauth_applications", force: :cascade do |t|
    t.string "name", null: false
    t.string "uid", null: false
    t.string "secret", null: false
    t.text "redirect_uri"
    t.string "scopes", default: "", null: false
    t.boolean "confidential", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_oauth_applications_on_uid", unique: true
  end

  create_table "organization_kpis", force: :cascade do |t|
    t.bigint "kpi_id", null: false
    t.integer "field_name"
    t.string "value"
    t.float "score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "actual"
    t.index ["kpi_id", "field_name", "value"], name: "index_organization_kpis_on_kpi_id_and_field_name_and_value", unique: true
    t.index ["kpi_id"], name: "index_organization_kpis_on_kpi_id"
  end

  create_table "organizations", force: :cascade do |t|
    t.string "main_company", null: false
    t.string "sub_company", null: false
    t.string "brand", null: false
    t.string "location_type", null: false
    t.string "location_id", null: false
    t.string "location_name", null: false
    t.string "country", null: false
    t.string "city", null: false
    t.string "zone"
    t.string "group_1"
    t.string "group_2"
    t.string "group_3"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "users_count", default: 0
    t.index ["location_id"], name: "index_organizations_on_location_id", unique: true
  end

  create_table "point_settings", force: :cascade do |t|
    t.bigint "point_id", null: false
    t.integer "supplier", null: false
    t.integer "conversion_rate", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["point_id", "supplier"], name: "index_point_settings_on_point_id_and_supplier", unique: true
    t.index ["point_id"], name: "index_point_settings_on_point_id"
  end

  create_table "points", force: :cascade do |t|
    t.string "code"
    t.string "name"
    t.boolean "is_active", default: false
    t.boolean "is_market", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "posts", force: :cascade do |t|
    t.string "postable_type", null: false
    t.bigint "postable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "impression_count", default: 0, null: false
    t.index ["postable_type", "postable_id"], name: "index_posts_on_postable"
    t.index ["postable_type", "postable_id"], name: "index_posts_on_postable_type_and_postable_id"
  end

  create_table "privacy_settings", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.integer "friend_list_visibility", null: false
    t.integer "challenge_invite_permission", null: false
    t.integer "kudos_list_visibility", null: false
    t.integer "badge_list_visibility", null: false
    t.integer "achievement_list_visibility", null: false
    t.integer "social_media_visibility", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_privacy_settings_on_user_id"
  end

  create_table "prize_awardables", force: :cascade do |t|
    t.bigint "prize_id", null: false
    t.string "awardable_type", null: false
    t.bigint "awardable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["awardable_type", "awardable_id"], name: "index_prize_awardables_on_awardable"
    t.index ["prize_id"], name: "index_prize_awardables_on_prize_id"
  end

  create_table "prize_point_limits", force: :cascade do |t|
    t.string "value", null: false
    t.bigint "prize_point_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "performer_type", default: 0, null: false
    t.index ["prize_point_id"], name: "index_prize_point_limits_on_prize_point_id"
  end

  create_table "prize_points", force: :cascade do |t|
    t.string "earnable_type", null: false
    t.bigint "earnable_id", null: false
    t.bigint "point_id", null: false
    t.integer "amount", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["earnable_type", "earnable_id"], name: "index_prize_points_on_earnable"
    t.index ["point_id"], name: "index_prize_points_on_point_id"
  end

  create_table "prizes", force: :cascade do |t|
    t.string "earnable_type", null: false
    t.bigint "earnable_id", null: false
    t.integer "activity_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["earnable_type", "earnable_id"], name: "index_prizes_on_earnable"
  end

  create_table "products", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.bigint "market_category_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "country", default: [], array: true
    t.jsonb "log_data"
    t.integer "supplier", default: 0, null: false
    t.integer "external_id"
    t.index ["external_id"], name: "index_products_on_external_id"
    t.index ["market_category_id"], name: "index_products_on_market_category_id"
  end

  create_table "purchase_options", force: :cascade do |t|
    t.integer "point_value"
    t.decimal "price_value"
    t.string "currency"
    t.boolean "is_active", default: false
    t.boolean "is_auto_delivery", default: false
    t.bigint "point_id", null: false
    t.bigint "product_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
    t.integer "external_id"
    t.index ["external_id"], name: "index_purchase_options_on_external_id"
    t.index ["point_id"], name: "index_purchase_options_on_point_id"
    t.index ["product_id"], name: "index_purchase_options_on_product_id"
  end

  create_table "related_channels", force: :cascade do |t|
    t.bigint "related_type_id", null: false
    t.bigint "related_field_id", null: false
    t.bigint "entity_type_id", null: false
    t.bigint "entity_field_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["entity_field_id"], name: "index_related_channels_on_entity_field_id"
    t.index ["entity_type_id"], name: "index_related_channels_on_entity_type_id"
    t.index ["related_field_id"], name: "index_related_channels_on_related_field_id"
    t.index ["related_type_id"], name: "index_related_channels_on_related_type_id"
  end

  create_table "reward_claim_items", force: :cascade do |t|
    t.bigint "reward_claim_id", null: false
    t.bigint "purchase_option_id", null: false
    t.datetime "occur_date", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "voucher_id"
    t.jsonb "log_data"
    t.jsonb "purchase_option_snapshot"
    t.string "reference_number"
    t.index ["purchase_option_id"], name: "index_reward_claim_items_on_purchase_option_id"
    t.index ["reference_number"], name: "index_reward_claim_items_on_reference_number", unique: true
    t.index ["reward_claim_id"], name: "index_reward_claim_items_on_reward_claim_id"
    t.index ["voucher_id"], name: "index_reward_claim_items_on_voucher_id"
  end

  create_table "reward_claims", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.datetime "request_date", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
    t.bigint "claimer_id"
    t.text "comment"
    t.index ["claimer_id"], name: "index_reward_claims_on_claimer_id"
    t.index ["user_id"], name: "index_reward_claims_on_user_id"
  end

  create_table "social_media_feeds", force: :cascade do |t|
    t.string "account_name", null: false
    t.integer "provider", null: false
    t.boolean "is_active", default: false, null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "metadata", default: {}
  end

  create_table "sub_groups", force: :cascade do |t|
    t.bigint "user_group_id", null: false
    t.integer "table_name"
    t.integer "field_name"
    t.text "values", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_group_id", "table_name", "field_name"], name: "index_sub_groups_on_user_group_id_and_table_name_and_field_name", unique: true
    t.index ["user_group_id"], name: "index_sub_groups_on_user_group_id"
  end

  create_table "tags", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.boolean "use_analytics", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "tmp_user_points", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "point_id", null: false
    t.bigint "mission_subscription_id"
    t.bigint "user_point_id"
    t.string "rewarder_type"
    t.bigint "rewarder_id"
    t.integer "amount", null: false
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_active", default: true
    t.index ["mission_subscription_id"], name: "index_tmp_user_points_on_mission_subscription_id"
    t.index ["point_id"], name: "index_tmp_user_points_on_point_id"
    t.index ["rewarder_type", "rewarder_id"], name: "index_tmp_user_points_on_rewarder"
    t.index ["user_id"], name: "index_tmp_user_points_on_user_id"
    t.index ["user_point_id"], name: "index_tmp_user_points_on_user_point_id"
  end

  create_table "tricks", force: :cascade do |t|
    t.string "tricks"
    t.text "how_to"
    t.datetime "publish_date"
    t.boolean "is_draft", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "category"
  end

  create_table "user_activities", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "level_id"
    t.integer "achievements_count", default: 0, null: false
    t.integer "badges_count", default: 0, null: false
    t.integer "points_count", default: 0, null: false
    t.integer "kudos_count", default: 0, null: false
    t.integer "add_friends_count", default: 0, null: false
    t.integer "win_challanges_count", default: 0, null: false
    t.integer "enps_replies_count", default: 0, null: false
    t.integer "vote_challanges_count", default: 0, null: false
    t.integer "social_posts_count", default: 0, null: false
    t.integer "survey_replies_count", default: 0, null: false
    t.integer "feedbacks_count", default: 0, null: false
    t.integer "likes_count", default: 0, null: false
    t.integer "start_challanges_count", default: 0, null: false
    t.integer "mission_completes_count", default: 0, null: false
    t.integer "mini_game_points_count", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
    t.index ["level_id"], name: "index_user_activities_on_level_id"
    t.index ["user_id"], name: "index_user_activities_on_user_id"
  end

  create_table "user_badges", force: :cascade do |t|
    t.bigint "badge_id", null: false
    t.bigint "user_id", null: false
    t.string "rewarder_type", null: false
    t.bigint "rewarder_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["badge_id"], name: "index_user_badges_on_badge_id"
    t.index ["rewarder_type", "rewarder_id"], name: "index_user_badges_on_rewarder"
    t.index ["user_id"], name: "index_user_badges_on_user_id"
  end

  create_table "user_groups", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.boolean "is_active", default: true
    t.boolean "default", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "user_kpis", force: :cascade do |t|
    t.string "user_id", null: false
    t.bigint "kpi_id", null: false
    t.float "target", null: false
    t.float "actual", null: false
    t.float "score", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["kpi_id"], name: "index_user_kpis_on_kpi_id"
    t.index ["user_id", "kpi_id"], name: "index_user_kpis_on_user_id_and_kpi_id", unique: true
  end

  create_table "user_kudos", force: :cascade do |t|
    t.bigint "kudo_id", null: false
    t.bigint "user_id", null: false
    t.string "rewarder_type", null: false
    t.bigint "rewarder_id", null: false
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "read", default: false
    t.index ["kudo_id"], name: "index_user_kudos_on_kudo_id"
    t.index ["rewarder_type", "rewarder_id"], name: "index_user_kudos_on_rewarder"
    t.index ["user_id"], name: "index_user_kudos_on_user_id"
  end

  create_table "user_levels", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "level_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
    t.index ["level_id"], name: "index_user_levels_on_level_id"
    t.index ["user_id", "level_id"], name: "index_user_levels_on_user_id_and_level_id", unique: true
    t.index ["user_id"], name: "index_user_levels_on_user_id"
  end

  create_table "user_mission_targets", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "mission_target_id", null: false
    t.string "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["mission_target_id"], name: "index_user_mission_targets_on_mission_target_id"
    t.index ["user_id", "mission_target_id"], name: "index_user_mission_targets_on_user_id_and_mission_target_id", unique: true
    t.index ["user_id"], name: "index_user_mission_targets_on_user_id"
  end

  create_table "user_points", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "point_id", null: false
    t.integer "amount", null: false
    t.string "description"
    t.string "rewarder_type", null: false
    t.bigint "rewarder_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["point_id"], name: "index_user_points_on_point_id"
    t.index ["rewarder_type", "rewarder_id"], name: "index_user_points_on_rewarder"
    t.index ["user_id"], name: "index_user_points_on_user_id"
  end

  create_table "user_tags", force: :cascade do |t|
    t.bigint "tag_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["tag_id"], name: "index_user_tags_on_tag_id"
    t.index ["user_id", "tag_id"], name: "index_user_tags_on_user_id_and_tag_id", unique: true
    t.index ["user_id"], name: "index_user_tags_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.integer "failed_attempts", default: 0, null: false
    t.string "unlock_token"
    t.datetime "locked_at"
    t.string "first_name"
    t.string "last_name"
    t.string "user_id", null: false
    t.integer "user_type", default: 0, null: false
    t.string "gsm_country_code"
    t.string "gsm_area_code"
    t.string "gsm_number"
    t.string "nickname"
    t.datetime "birthdate"
    t.datetime "work_anniversary"
    t.string "department"
    t.string "title", null: false
    t.integer "gender"
    t.integer "status", default: 0, null: false
    t.string "activation_code"
    t.string "location_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "activation_code_expires_at"
    t.integer "user_kudos_count", default: 0, null: false
    t.integer "user_badges_count", default: 0, null: false
    t.integer "given_user_kudos_count", default: 0, null: false
    t.jsonb "social_accounts", default: {"twitter"=>"", "facebook"=>"", "linkedin"=>"", "instagram"=>""}, null: false
    t.string "fcm_token"
    t.string "app_language", default: "en"
    t.index ["activation_code"], name: "index_users_on_activation_code", unique: true, where: "(activation_code IS NOT NULL)"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["location_id"], name: "index_users_on_location_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["unlock_token"], name: "index_users_on_unlock_token", unique: true
    t.index ["user_id"], name: "index_users_on_user_id", unique: true
  end

  create_table "vouchers", force: :cascade do |t|
    t.integer "status", default: 0, null: false
    t.string "code", null: false
    t.string "password"
    t.date "expire_date"
    t.bigint "purchase_option_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "log_data"
    t.index ["code"], name: "index_vouchers_on_code", unique: true
    t.index ["purchase_option_id"], name: "index_vouchers_on_purchase_option_id"
  end

  add_foreign_key "achievement_gradual_users", "achievement_graduals"
  add_foreign_key "achievement_gradual_users", "users"
  add_foreign_key "achievement_graduals", "achievements"
  add_foreign_key "achievement_sources", "achievements"
  add_foreign_key "achievement_subscriptions", "achievement_graduals", column: "gradual_level_id"
  add_foreign_key "achievement_subscriptions", "achievements"
  add_foreign_key "achievement_subscriptions", "users"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "beeply_metrics", "entity_fields", column: "calculate_from_id"
  add_foreign_key "beeply_metrics", "entity_types", column: "data_channel_id"
  add_foreign_key "celebrations", "users"
  add_foreign_key "challenge_options", "mission_awards"
  add_foreign_key "challenge_options", "missions"
  add_foreign_key "challenge_settings", "points", column: "challenge_point_id"
  add_foreign_key "challenge_settings", "points", column: "vote_point_id"
  add_foreign_key "default_settings", "languages"
  add_foreign_key "default_settings", "points", column: "level_point_id"
  add_foreign_key "entity_fields", "entity_types"
  add_foreign_key "entity_record_values", "entity_fields"
  add_foreign_key "entity_record_values", "entity_records"
  add_foreign_key "entity_records", "entity_types"
  add_foreign_key "entity_types", "entity_fields", column: "primary_user_id"
  add_foreign_key "fetch_posts", "social_media_feeds"
  add_foreign_key "friendship_challenge_votes", "friendship_challenges"
  add_foreign_key "friendship_challenge_votes", "users"
  add_foreign_key "friendship_requests", "users", column: "receiver_id"
  add_foreign_key "friendship_requests", "users", column: "requestor_id"
  add_foreign_key "friendships", "users"
  add_foreign_key "friendships", "users", column: "friend_id"
  add_foreign_key "integration_excel_accesses", "users", column: "creator_id"
  add_foreign_key "integration_import_achievement_data", "integrations"
  add_foreign_key "integration_import_achievement_data", "users"
  add_foreign_key "integration_import_kpi_data", "integrations"
  add_foreign_key "integration_import_kpi_data", "users"
  add_foreign_key "integration_import_mission_data", "integrations"
  add_foreign_key "integration_import_mission_data", "users"
  add_foreign_key "integration_periods", "integrations"
  add_foreign_key "kpi_related_missions", "kpis"
  add_foreign_key "kpi_related_missions", "missions"
  add_foreign_key "kudo_limits", "kudos"
  add_foreign_key "level_awards", "levels"
  add_foreign_key "mission_awards", "missions"
  add_foreign_key "mission_periods", "missions", column: "main_mission_id"
  add_foreign_key "mission_subscriptions", "users"
  add_foreign_key "mission_targets", "missions"
  add_foreign_key "nft_certificates", "users"
  add_foreign_key "notifications", "users"
  add_foreign_key "oauth_access_grants", "oauth_applications", column: "application_id"
  add_foreign_key "oauth_access_tokens", "oauth_applications", column: "application_id"
  add_foreign_key "organization_kpis", "kpis"
  add_foreign_key "privacy_settings", "users"
  add_foreign_key "prize_awardables", "prizes"
  add_foreign_key "prize_point_limits", "prize_points"
  add_foreign_key "prize_points", "points"
  add_foreign_key "products", "market_categories"
  add_foreign_key "related_channels", "entity_fields"
  add_foreign_key "related_channels", "entity_fields", column: "related_field_id"
  add_foreign_key "related_channels", "entity_types"
  add_foreign_key "related_channels", "entity_types", column: "related_type_id"
  add_foreign_key "reward_claim_items", "purchase_options"
  add_foreign_key "reward_claim_items", "reward_claims"
  add_foreign_key "reward_claim_items", "vouchers"
  add_foreign_key "reward_claims", "users"
  add_foreign_key "reward_claims", "users", column: "claimer_id"
  add_foreign_key "sub_groups", "user_groups"
  add_foreign_key "tmp_user_points", "mission_subscriptions"
  add_foreign_key "tmp_user_points", "points"
  add_foreign_key "tmp_user_points", "user_points"
  add_foreign_key "tmp_user_points", "users"
  add_foreign_key "user_activities", "levels"
  add_foreign_key "user_activities", "users"
  add_foreign_key "user_badges", "badges"
  add_foreign_key "user_badges", "users"
  add_foreign_key "user_kpis", "kpis"
  add_foreign_key "user_kpis", "users", primary_key: "user_id"
  add_foreign_key "user_kudos", "kudos"
  add_foreign_key "user_kudos", "users"
  add_foreign_key "user_levels", "levels"
  add_foreign_key "user_levels", "users"
  add_foreign_key "user_mission_targets", "mission_targets"
  add_foreign_key "user_mission_targets", "users"
  add_foreign_key "user_points", "points"
  add_foreign_key "user_points", "users"
  add_foreign_key "user_tags", "tags"
  add_foreign_key "user_tags", "users"
  add_foreign_key "vouchers", "purchase_options"
  create_function :logidze_capture_exception, sql_definition: <<-'SQL'
      CREATE OR REPLACE FUNCTION public.logidze_capture_exception(error_data jsonb)
       RETURNS boolean
       LANGUAGE plpgsql
      AS $function$
        -- version: 1
      BEGIN
        -- Feel free to change this function to change Logidze behavior on exception.
        --
        -- Return `false` to raise exception or `true` to commit record changes.
        --
        -- `error_data` contains:
        --   - returned_sqlstate
        --   - message_text
        --   - pg_exception_detail
        --   - pg_exception_hint
        --   - pg_exception_context
        --   - schema_name
        --   - table_name
        -- Learn more about available keys:
        -- https://www.postgresql.org/docs/9.6/plpgsql-control-structures.html#PLPGSQL-EXCEPTION-DIAGNOSTICS-VALUES
        --

        return false;
      END;
      $function$
  SQL
  create_function :logidze_compact_history, sql_definition: <<-'SQL'
      CREATE OR REPLACE FUNCTION public.logidze_compact_history(log_data jsonb, cutoff integer DEFAULT 1)
       RETURNS jsonb
       LANGUAGE plpgsql
      AS $function$
        -- version: 1
        DECLARE
          merged jsonb;
        BEGIN
          LOOP
            merged := jsonb_build_object(
              'ts',
              log_data#>'{h,1,ts}',
              'v',
              log_data#>'{h,1,v}',
              'c',
              (log_data#>'{h,0,c}') || (log_data#>'{h,1,c}')
            );

            IF (log_data#>'{h,1}' ? 'm') THEN
              merged := jsonb_set(merged, ARRAY['m'], log_data#>'{h,1,m}');
            END IF;

            log_data := jsonb_set(
              log_data,
              '{h}',
              jsonb_set(
                log_data->'h',
                '{1}',
                merged
              ) - 0
            );

            cutoff := cutoff - 1;

            EXIT WHEN cutoff <= 0;
          END LOOP;

          return log_data;
        END;
      $function$
  SQL
  create_function :logidze_filter_keys, sql_definition: <<-'SQL'
      CREATE OR REPLACE FUNCTION public.logidze_filter_keys(obj jsonb, keys text[], include_columns boolean DEFAULT false)
       RETURNS jsonb
       LANGUAGE plpgsql
      AS $function$
        -- version: 1
        DECLARE
          res jsonb;
          key text;
        BEGIN
          res := '{}';

          IF include_columns THEN
            FOREACH key IN ARRAY keys
            LOOP
              IF obj ? key THEN
                res = jsonb_insert(res, ARRAY[key], obj->key);
              END IF;
            END LOOP;
          ELSE
            res = obj;
            FOREACH key IN ARRAY keys
            LOOP
              res = res - key;
            END LOOP;
          END IF;

          RETURN res;
        END;
      $function$
  SQL
  create_function :logidze_logger, sql_definition: <<-'SQL'
      CREATE OR REPLACE FUNCTION public.logidze_logger()
       RETURNS trigger
       LANGUAGE plpgsql
      AS $function$
        -- version: 4
        DECLARE
          changes jsonb;
          version jsonb;
          full_snapshot boolean;
          log_data jsonb;
          new_v integer;
          size integer;
          history_limit integer;
          debounce_time integer;
          current_version integer;
          k text;
          iterator integer;
          item record;
          columns text[];
          include_columns boolean;
          ts timestamp with time zone;
          ts_column text;
          err_sqlstate text;
          err_message text;
          err_detail text;
          err_hint text;
          err_context text;
          err_table_name text;
          err_schema_name text;
          err_jsonb jsonb;
          err_captured boolean;
        BEGIN
          ts_column := NULLIF(TG_ARGV[1], 'null');
          columns := NULLIF(TG_ARGV[2], 'null');
          include_columns := NULLIF(TG_ARGV[3], 'null');

          IF NEW.log_data is NULL OR NEW.log_data = '{}'::jsonb
          THEN
            IF columns IS NOT NULL THEN
              log_data = logidze_snapshot(to_jsonb(NEW.*), ts_column, columns, include_columns);
            ELSE
              log_data = logidze_snapshot(to_jsonb(NEW.*), ts_column);
            END IF;

            IF log_data#>>'{h, -1, c}' != '{}' THEN
              NEW.log_data := log_data;
            END IF;

          ELSE

            IF TG_OP = 'UPDATE' AND (to_jsonb(NEW.*) = to_jsonb(OLD.*)) THEN
              RETURN NEW; -- pass
            END IF;

            history_limit := NULLIF(TG_ARGV[0], 'null');
            debounce_time := NULLIF(TG_ARGV[4], 'null');

            log_data := NEW.log_data;

            current_version := (log_data->>'v')::int;

            IF ts_column IS NULL THEN
              ts := statement_timestamp();
            ELSEIF TG_OP = 'UPDATE' THEN
              ts := (to_jsonb(NEW.*) ->> ts_column)::timestamp with time zone;
              IF ts IS NULL OR ts = (to_jsonb(OLD.*) ->> ts_column)::timestamp with time zone THEN
                ts := statement_timestamp();
              END IF;
            ELSEIF TG_OP = 'INSERT' THEN
              ts := (to_jsonb(NEW.*) ->> ts_column)::timestamp with time zone;
              IF ts IS NULL OR (extract(epoch from ts) * 1000)::bigint = (NEW.log_data #>> '{h,-1,ts}')::bigint THEN
                ts := statement_timestamp();
              END IF;
            END IF;

            full_snapshot := (coalesce(current_setting('logidze.full_snapshot', true), '') = 'on') OR (TG_OP = 'INSERT');

            IF current_version < (log_data#>>'{h,-1,v}')::int THEN
              iterator := 0;
              FOR item in SELECT * FROM jsonb_array_elements(log_data->'h')
              LOOP
                IF (item.value->>'v')::int > current_version THEN
                  log_data := jsonb_set(
                    log_data,
                    '{h}',
                    (log_data->'h') - iterator
                  );
                END IF;
                iterator := iterator + 1;
              END LOOP;
            END IF;

            changes := '{}';

            IF full_snapshot THEN
              BEGIN
                changes = hstore_to_jsonb_loose(hstore(NEW.*));
              EXCEPTION
                WHEN NUMERIC_VALUE_OUT_OF_RANGE THEN
                  changes = row_to_json(NEW.*)::jsonb;
                  FOR k IN (SELECT key FROM jsonb_each(changes))
                  LOOP
                    IF jsonb_typeof(changes->k) = 'object' THEN
                      changes = jsonb_set(changes, ARRAY[k], to_jsonb(changes->>k));
                    END IF;
                  END LOOP;
              END;
            ELSE
              BEGIN
                changes = hstore_to_jsonb_loose(
                      hstore(NEW.*) - hstore(OLD.*)
                  );
              EXCEPTION
                WHEN NUMERIC_VALUE_OUT_OF_RANGE THEN
                  changes = (SELECT
                    COALESCE(json_object_agg(key, value), '{}')::jsonb
                    FROM
                    jsonb_each(row_to_json(NEW.*)::jsonb)
                    WHERE NOT jsonb_build_object(key, value) <@ row_to_json(OLD.*)::jsonb);
                  FOR k IN (SELECT key FROM jsonb_each(changes))
                  LOOP
                    IF jsonb_typeof(changes->k) = 'object' THEN
                      changes = jsonb_set(changes, ARRAY[k], to_jsonb(changes->>k));
                    END IF;
                  END LOOP;
              END;
            END IF;

            changes = changes - 'log_data';

            IF columns IS NOT NULL THEN
              changes = logidze_filter_keys(changes, columns, include_columns);
            END IF;

            IF changes = '{}' THEN
              RETURN NEW; -- pass
            END IF;

            new_v := (log_data#>>'{h,-1,v}')::int + 1;

            size := jsonb_array_length(log_data->'h');
            version := logidze_version(new_v, changes, ts);

            IF (
              debounce_time IS NOT NULL AND
              (version->>'ts')::bigint - (log_data#>'{h,-1,ts}')::text::bigint <= debounce_time
            ) THEN
              -- merge new version with the previous one
              new_v := (log_data#>>'{h,-1,v}')::int;
              version := logidze_version(new_v, (log_data#>'{h,-1,c}')::jsonb || changes, ts);
              -- remove the previous version from log
              log_data := jsonb_set(
                log_data,
                '{h}',
                (log_data->'h') - (size - 1)
              );
            END IF;

            log_data := jsonb_set(
              log_data,
              ARRAY['h', size::text],
              version,
              true
            );

            log_data := jsonb_set(
              log_data,
              '{v}',
              to_jsonb(new_v)
            );

            IF history_limit IS NOT NULL AND history_limit <= size THEN
              log_data := logidze_compact_history(log_data, size - history_limit + 1);
            END IF;

            NEW.log_data := log_data;
          END IF;

          RETURN NEW; -- result
        EXCEPTION
          WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS err_sqlstate = RETURNED_SQLSTATE,
                                    err_message = MESSAGE_TEXT,
                                    err_detail = PG_EXCEPTION_DETAIL,
                                    err_hint = PG_EXCEPTION_HINT,
                                    err_context = PG_EXCEPTION_CONTEXT,
                                    err_schema_name = SCHEMA_NAME,
                                    err_table_name = TABLE_NAME;
            err_jsonb := jsonb_build_object(
              'returned_sqlstate', err_sqlstate,
              'message_text', err_message,
              'pg_exception_detail', err_detail,
              'pg_exception_hint', err_hint,
              'pg_exception_context', err_context,
              'schema_name', err_schema_name,
              'table_name', err_table_name
            );
            err_captured = logidze_capture_exception(err_jsonb);
            IF err_captured THEN
              return NEW;
            ELSE
              RAISE;
            END IF;
        END;
      $function$
  SQL
  create_function :logidze_logger_after, sql_definition: <<-'SQL'
      CREATE OR REPLACE FUNCTION public.logidze_logger_after()
       RETURNS trigger
       LANGUAGE plpgsql
      AS $function$
        -- version: 4


        DECLARE
          changes jsonb;
          version jsonb;
          full_snapshot boolean;
          log_data jsonb;
          new_v integer;
          size integer;
          history_limit integer;
          debounce_time integer;
          current_version integer;
          k text;
          iterator integer;
          item record;
          columns text[];
          include_columns boolean;
          ts timestamp with time zone;
          ts_column text;
          err_sqlstate text;
          err_message text;
          err_detail text;
          err_hint text;
          err_context text;
          err_table_name text;
          err_schema_name text;
          err_jsonb jsonb;
          err_captured boolean;
        BEGIN
          ts_column := NULLIF(TG_ARGV[1], 'null');
          columns := NULLIF(TG_ARGV[2], 'null');
          include_columns := NULLIF(TG_ARGV[3], 'null');

          IF NEW.log_data is NULL OR NEW.log_data = '{}'::jsonb
          THEN
            IF columns IS NOT NULL THEN
              log_data = logidze_snapshot(to_jsonb(NEW.*), ts_column, columns, include_columns);
            ELSE
              log_data = logidze_snapshot(to_jsonb(NEW.*), ts_column);
            END IF;

            IF log_data#>>'{h, -1, c}' != '{}' THEN
              NEW.log_data := log_data;
            END IF;

          ELSE

            IF TG_OP = 'UPDATE' AND (to_jsonb(NEW.*) = to_jsonb(OLD.*)) THEN
              RETURN NULL;
            END IF;

            history_limit := NULLIF(TG_ARGV[0], 'null');
            debounce_time := NULLIF(TG_ARGV[4], 'null');

            log_data := NEW.log_data;

            current_version := (log_data->>'v')::int;

            IF ts_column IS NULL THEN
              ts := statement_timestamp();
            ELSEIF TG_OP = 'UPDATE' THEN
              ts := (to_jsonb(NEW.*) ->> ts_column)::timestamp with time zone;
              IF ts IS NULL OR ts = (to_jsonb(OLD.*) ->> ts_column)::timestamp with time zone THEN
                ts := statement_timestamp();
              END IF;
            ELSEIF TG_OP = 'INSERT' THEN
              ts := (to_jsonb(NEW.*) ->> ts_column)::timestamp with time zone;
              IF ts IS NULL OR (extract(epoch from ts) * 1000)::bigint = (NEW.log_data #>> '{h,-1,ts}')::bigint THEN
                ts := statement_timestamp();
              END IF;
            END IF;

            full_snapshot := (coalesce(current_setting('logidze.full_snapshot', true), '') = 'on') OR (TG_OP = 'INSERT');

            IF current_version < (log_data#>>'{h,-1,v}')::int THEN
              iterator := 0;
              FOR item in SELECT * FROM jsonb_array_elements(log_data->'h')
              LOOP
                IF (item.value->>'v')::int > current_version THEN
                  log_data := jsonb_set(
                    log_data,
                    '{h}',
                    (log_data->'h') - iterator
                  );
                END IF;
                iterator := iterator + 1;
              END LOOP;
            END IF;

            changes := '{}';

            IF full_snapshot THEN
              BEGIN
                changes = hstore_to_jsonb_loose(hstore(NEW.*));
              EXCEPTION
                WHEN NUMERIC_VALUE_OUT_OF_RANGE THEN
                  changes = row_to_json(NEW.*)::jsonb;
                  FOR k IN (SELECT key FROM jsonb_each(changes))
                  LOOP
                    IF jsonb_typeof(changes->k) = 'object' THEN
                      changes = jsonb_set(changes, ARRAY[k], to_jsonb(changes->>k));
                    END IF;
                  END LOOP;
              END;
            ELSE
              BEGIN
                changes = hstore_to_jsonb_loose(
                      hstore(NEW.*) - hstore(OLD.*)
                  );
              EXCEPTION
                WHEN NUMERIC_VALUE_OUT_OF_RANGE THEN
                  changes = (SELECT
                    COALESCE(json_object_agg(key, value), '{}')::jsonb
                    FROM
                    jsonb_each(row_to_json(NEW.*)::jsonb)
                    WHERE NOT jsonb_build_object(key, value) <@ row_to_json(OLD.*)::jsonb);
                  FOR k IN (SELECT key FROM jsonb_each(changes))
                  LOOP
                    IF jsonb_typeof(changes->k) = 'object' THEN
                      changes = jsonb_set(changes, ARRAY[k], to_jsonb(changes->>k));
                    END IF;
                  END LOOP;
              END;
            END IF;

            changes = changes - 'log_data';

            IF columns IS NOT NULL THEN
              changes = logidze_filter_keys(changes, columns, include_columns);
            END IF;

            IF changes = '{}' THEN
              RETURN NULL;
            END IF;

            new_v := (log_data#>>'{h,-1,v}')::int + 1;

            size := jsonb_array_length(log_data->'h');
            version := logidze_version(new_v, changes, ts);

            IF (
              debounce_time IS NOT NULL AND
              (version->>'ts')::bigint - (log_data#>'{h,-1,ts}')::text::bigint <= debounce_time
            ) THEN
              -- merge new version with the previous one
              new_v := (log_data#>>'{h,-1,v}')::int;
              version := logidze_version(new_v, (log_data#>'{h,-1,c}')::jsonb || changes, ts);
              -- remove the previous version from log
              log_data := jsonb_set(
                log_data,
                '{h}',
                (log_data->'h') - (size - 1)
              );
            END IF;

            log_data := jsonb_set(
              log_data,
              ARRAY['h', size::text],
              version,
              true
            );

            log_data := jsonb_set(
              log_data,
              '{v}',
              to_jsonb(new_v)
            );

            IF history_limit IS NOT NULL AND history_limit <= size THEN
              log_data := logidze_compact_history(log_data, size - history_limit + 1);
            END IF;

            NEW.log_data := log_data;
          END IF;

              EXECUTE format('UPDATE %I.%I SET "log_data" = $1 WHERE ctid = %L', TG_TABLE_SCHEMA, TG_TABLE_NAME, NEW.CTID) USING NEW.log_data;
          RETURN NULL;
        EXCEPTION
          WHEN OTHERS THEN
            GET STACKED DIAGNOSTICS err_sqlstate = RETURNED_SQLSTATE,
                                    err_message = MESSAGE_TEXT,
                                    err_detail = PG_EXCEPTION_DETAIL,
                                    err_hint = PG_EXCEPTION_HINT,
                                    err_context = PG_EXCEPTION_CONTEXT,
                                    err_schema_name = SCHEMA_NAME,
                                    err_table_name = TABLE_NAME;
            err_jsonb := jsonb_build_object(
              'returned_sqlstate', err_sqlstate,
              'message_text', err_message,
              'pg_exception_detail', err_detail,
              'pg_exception_hint', err_hint,
              'pg_exception_context', err_context,
              'schema_name', err_schema_name,
              'table_name', err_table_name
            );
            err_captured = logidze_capture_exception(err_jsonb);
            IF err_captured THEN
              return NEW;
            ELSE
              RAISE;
            END IF;
        END;
      $function$
  SQL
  create_function :logidze_snapshot, sql_definition: <<-'SQL'
      CREATE OR REPLACE FUNCTION public.logidze_snapshot(item jsonb, ts_column text DEFAULT NULL::text, columns text[] DEFAULT NULL::text[], include_columns boolean DEFAULT false)
       RETURNS jsonb
       LANGUAGE plpgsql
      AS $function$
        -- version: 3
        DECLARE
          ts timestamp with time zone;
          k text;
        BEGIN
          item = item - 'log_data';
          IF ts_column IS NULL THEN
            ts := statement_timestamp();
          ELSE
            ts := coalesce((item->>ts_column)::timestamp with time zone, statement_timestamp());
          END IF;

          IF columns IS NOT NULL THEN
            item := logidze_filter_keys(item, columns, include_columns);
          END IF;

          FOR k IN (SELECT key FROM jsonb_each(item))
          LOOP
            IF jsonb_typeof(item->k) = 'object' THEN
               item := jsonb_set(item, ARRAY[k], to_jsonb(item->>k));
            END IF;
          END LOOP;

          return json_build_object(
            'v', 1,
            'h', jsonb_build_array(
                    logidze_version(1, item, ts)
                  )
            );
        END;
      $function$
  SQL
  create_function :logidze_version, sql_definition: <<-'SQL'
      CREATE OR REPLACE FUNCTION public.logidze_version(v bigint, data jsonb, ts timestamp with time zone)
       RETURNS jsonb
       LANGUAGE plpgsql
      AS $function$
        -- version: 2
        DECLARE
          buf jsonb;
        BEGIN
          data = data - 'log_data';
          buf := jsonb_build_object(
                    'ts',
                    (extract(epoch from ts) * 1000)::bigint,
                    'v',
                    v,
                    'c',
                    data
                    );
          IF coalesce(current_setting('logidze.meta', true), '') <> '' THEN
            buf := jsonb_insert(buf, '{m}', current_setting('logidze.meta')::jsonb);
          END IF;
          RETURN buf;
        END;
      $function$
  SQL


  create_trigger :logidze_on_achievement_graduals, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_achievement_graduals BEFORE INSERT OR UPDATE ON public.achievement_graduals FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_achievement_sources, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_achievement_sources BEFORE INSERT OR UPDATE ON public.achievement_sources FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_achievement_subscriptions, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_achievement_subscriptions BEFORE INSERT OR UPDATE ON public.achievement_subscriptions FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_achievements, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_achievements BEFORE INSERT OR UPDATE ON public.achievements FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_challenge_options, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_challenge_options BEFORE INSERT OR UPDATE ON public.challenge_options FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_friendship_challenge_votes, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_friendship_challenge_votes BEFORE INSERT OR UPDATE ON public.friendship_challenge_votes FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_friendship_challenges, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_friendship_challenges BEFORE INSERT OR UPDATE ON public.friendship_challenges FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_level_awards, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_level_awards BEFORE INSERT OR UPDATE ON public.level_awards FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_levels, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_levels BEFORE INSERT OR UPDATE ON public.levels FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_mission_awards, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_mission_awards BEFORE INSERT OR UPDATE ON public.mission_awards FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_mission_periods, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_mission_periods BEFORE INSERT OR UPDATE ON public.mission_periods FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_mission_subscriptions, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_mission_subscriptions BEFORE INSERT OR UPDATE ON public.mission_subscriptions FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_mission_targets, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_mission_targets BEFORE INSERT OR UPDATE ON public.mission_targets FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_missions, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_missions BEFORE INSERT OR UPDATE ON public.missions FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_products, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_products BEFORE INSERT OR UPDATE ON public.products FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_purchase_options, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_purchase_options BEFORE INSERT OR UPDATE ON public.purchase_options FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_reward_claim_items, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_reward_claim_items BEFORE INSERT OR UPDATE ON public.reward_claim_items FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_reward_claims, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_reward_claims BEFORE INSERT OR UPDATE ON public.reward_claims FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_user_activities, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_user_activities BEFORE INSERT OR UPDATE ON public.user_activities FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_user_levels, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_user_levels BEFORE INSERT OR UPDATE ON public.user_levels FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
  create_trigger :logidze_on_vouchers, sql_definition: <<-SQL
      CREATE TRIGGER logidze_on_vouchers BEFORE INSERT OR UPDATE ON public.vouchers FOR EACH ROW WHEN ((COALESCE(current_setting('logidze.disabled'::text, true), ''::text) <> 'on'::text)) EXECUTE FUNCTION logidze_logger('null', 'updated_at')
  SQL
end
